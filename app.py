# Import necessary libraries
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
from peft import PeftModel
from flask import Flask, request, render_template
import gc
import logging

# Set up logging to monitor application behavior
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Configure 4-bit quantization to reduce memory usage
bnb_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_use_double_quant=False,
    bnb_4bit_quant_type="nf4",
    bnb_4bit_compute_dtype=torch.bfloat16,
)

# Load base model and tokenizer
logger.info("Loading base model and tokenizer...")
base_model_dir = "Qwen/Qwen3-32B"  # Base model used for fine-tuning
try:
    tokenizer = AutoTokenizer.from_pretrained(base_model_dir, use_fast=True)
    base_model = AutoModelForCausalLM.from_pretrained(
        base_model_dir,
        quantization_config=bnb_config,
        device_map="auto",
        torch_dtype=torch.bfloat16,
        trust_remote_code=True
    )
    logger.info(f"Successfully loaded base model: {base_model_dir}")
except Exception as e:
    logger.error(f"Error loading base model: {e}")
    logger.error("Verify that 'Qwen/Qwen3-32B' exists on Hugging Face or use a valid model ID (e.g., 'Qwen/Qwen2-72B-Instruct').")
    raise

# Clear memory
gc.collect()
torch.cuda.empty_cache()
logger.info("Memory cleared after loading base model")
logger.info(f"GPU memory allocated: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
logger.info(f"GPU memory reserved: {torch.cuda.memory_reserved() / 1024**3:.2f} GB")

# Load fine-tuned LoRA adapters from Hugging Face
logger.info("Loading fine-tuned LoRA adapters...")
finetuned_model_dir = "Qwen3-32B-Medical-Triage"  # Fine-tuned model name
try:
    model = PeftModel.from_pretrained(
        base_model,
        finetuned_model_dir,
        device_map="auto",
        torch_dtype=torch.bfloat16,
        trust_remote_code=True
    )
    logger.info(f"Successfully loaded fine-tuned LoRA adapters: {finetuned_model_dir}")
except Exception as e:
    logger.error(f"Error loading fine-tuned model: {e}")
    logger.error(f"Ensure '{finetuned_model_dir}' exists on Hugging Face and you have access.")
    raise

# Merge LoRA adapters with base model for inference
model = model.merge_and_unload()
logger.info("LoRA adapters merged with base model for inference")

# Clear memory
gc.collect()
torch.cuda.empty_cache()
logger.info("Memory cleared after loading fine-tuned model")
logger.info(f"GPU memory allocated: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
logger.info(f"GPU memory reserved: {torch.cuda.memory_reserved() / 1024**3:.2f} GB")

# Define inference prompt template (matches fine-tuning prompt style)
inference_prompt_style = """Below is an instruction that describes a task, paired with an input that provides further context. 
Write a response that appropriately completes the request. 
Before answering, think carefully about the question and create a step-by-step chain of thoughts to ensure a logical and accurate response.

### Instruction:
You are a medical expert with advanced knowledge in clinical reasoning, diagnostics, and treatment planning. 
Please answer the following medical question. 

### Question:
{question}

### Response:
<think>
"""

# Route for the homepage
@app.route("/", methods=["GET", "POST"])
def index():
    if request.method == "POST":
        # Get user input from the form
        user_question = request.form.get("question")
        logger.info(f"Received user question: {user_question}")

        if not user_question:
            logger.warning("No question provided by user")
            return render_template("index.html", error="Please provide a question.", question="", response="")

        # Prepare input for inference
        try:
            inputs = tokenizer(
                [inference_prompt_style.format(question=user_question)],
                return_tensors="pt"
            ).to("cuda")
            logger.info("Input prepared for inference")
        except Exception as e:
            logger.error(f"Error preparing input: {e}")
            return render_template("index.html", error=f"Error preparing input: {e}", question=user_question, response="")

        # Generate response
        try:
            outputs = model.generate(
                input_ids=inputs.input_ids,
                attention_mask=inputs.attention_mask,
                max_new_tokens=1200,
                eos_token_id=tokenizer.eos_token_id,
                use_cache=True,
            )
            response = tokenizer.batch_decode(outputs, skip_special_tokens=True)[0]
            # Extract the response after the "### Response:" marker
            response_text = response.split("### Response:")[1].strip()
            logger.info("Successfully generated response")
        except Exception as e:
            logger.error(f"Error during inference: {e}")
            return render_template("index.html", error=f"Error during inference: {e}", question=user_question, response="")

        # Clear memory after inference
        gc.collect()
        torch.cuda.empty_cache()
        logger.info("Memory cleared after inference")

        # Render the template with the question and response
        return render_template("index.html", question=user_question, response=response_text, error=None)

    # For GET requests, render the empty form
    return render_template("index.html", question="", response="", error=None)

# Run the Flask app
if __name__ == "__main__":
    logger.info("Starting Flask application...")
    app.run(host="0.0.0.0", port=5000, debug=False)